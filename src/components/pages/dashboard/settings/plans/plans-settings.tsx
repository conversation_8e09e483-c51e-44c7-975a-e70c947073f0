"use client";

import { useState } from "react";
import {
  Receipt,
  CheckCircle,
  AlertCircle,
  CreditCard,
  Clock,
  Zap,
  Shield,
  Users,
  Package,
  Star,
  Info,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SubscriptionPlan } from "@prisma/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";

interface PlansSettingsProps {
  initialData?: {
    plan: SubscriptionPlan;
    expiryDate: string | null;
    isActive: boolean;
  };
}

export default function PlansSettings({ initialData }: PlansSettingsProps) {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    initialData?.plan || null
  );

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("id-ID", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Get plan details from centralized configuration
  const getPlanDetails = (plan: SubscriptionPlan) => {
    const planConfig = SUBSCRIPTION_PLANS[plan];
    if (!planConfig) {
      return {
        name: "Tidak diketahui",
        price: "N/A",
        features: [],
        limitations: [],
      };
    }

    // Format price for display
    const price =
      planConfig.price === 0
        ? "Rp 0"
        : `Rp ${planConfig.price.toLocaleString("id-ID")}/${planConfig.period.replace("per ", "")}`;

    return {
      name: planConfig.name,
      price,
      features: planConfig.features,
      limitations: planConfig.limitations,
    };
  };

  const currentPlanDetails = initialData
    ? getPlanDetails(initialData.plan)
    : getPlanDetails("FREE");

  // Handle subscription upgrade
  const handleSubscribe = async (plan: SubscriptionPlan) => {
    if (plan === initialData?.plan) {
      toast.info("Anda sudah menggunakan paket ini");
      return;
    }

    setSelectedPlan(plan);

    // Show loading toast
    const loadingToast = toast.loading(
      `Memproses paket ${getPlanDetails(plan).name}...`
    );

    try {
      // Call the API to create a subscription
      const response = await fetch("/api/subscriptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ plan }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Gagal berlangganan");
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (plan === "FREE") {
        // For free plan, just show success message and refresh
        toast.success("Berhasil beralih ke paket Gratis");
        router.refresh();
      } else if (data.invoiceUrl) {
        // For paid plans, redirect to payment page
        toast.success(`Mengarahkan ke halaman pembayaran...`);
        window.location.href = data.invoiceUrl;
      } else {
        // Fallback success message
        toast.success(`Berhasil memilih paket ${getPlanDetails(plan).name}`);
        router.refresh();
      }
    } catch (error) {
      // Dismiss loading toast and show error
      toast.dismiss(loadingToast);
      console.error("Error subscribing:", error);
      toast.error("Gagal berlangganan. Silakan coba lagi.");
    }
  };

  return (
    <div className="space-y-6">
      {/* Modern Header */}
      <Card className="border-none shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Receipt className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Plan & Tagihan</h2>
                <p className="text-sm text-muted-foreground">
                  Kelola langganan dan metode pembayaran Anda
                </p>
              </div>
            </div>

            {initialData && (
              <div className="flex items-center gap-2 px-3 py-1.5 bg-primary/10 rounded-full text-sm">
                <span>Paket Saat Ini:</span>
                <Badge variant="outline" className="font-medium bg-background">
                  {currentPlanDetails.name}
                </Badge>
              </div>
            )}
          </div>
        </CardHeader>

        {/* Subscription Summary */}
        {initialData && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                  <Clock className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Aktif Hingga</p>
                  <p className="font-medium">
                    {initialData.expiryDate
                      ? formatDate(initialData.expiryDate)
                      : "Selamanya"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <CreditCard className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Harga</p>
                  <p className="font-medium">{currentPlanDetails.price}</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-background rounded-lg border shadow-sm">
                <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                  <Zap className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Status</p>
                  <p className="font-medium">
                    {initialData.isActive ? "Aktif" : "Tidak Aktif"}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Current Plan Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-medium">Langganan Saat Ini</h3>
            {initialData?.isActive && (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                Aktif
              </Badge>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/dashboard/settings/billing")}
            className="flex items-center gap-1.5 cursor-pointer"
          >
            <Receipt className="h-4 w-4" />
            Riwayat Pembayaran
          </Button>
        </div>

        <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
          <div className="flex flex-col md:flex-row">
            {/* Left side - Plan info */}
            <div className="p-6 md:w-2/3">
              <div className="flex items-center gap-3 mb-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <Star className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium text-lg">
                    Paket {currentPlanDetails.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {initialData?.isActive
                      ? `Aktif hingga ${formatDate(initialData.expiryDate)}`
                      : "Tidak aktif"}
                  </p>
                </div>
              </div>

              <div className="space-y-3 mt-6">
                {currentPlanDetails.features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}

                {currentPlanDetails.limitations.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-dashed">
                    {currentPlanDetails.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-start gap-2 mt-2">
                        <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                        <span className="text-sm text-muted-foreground">
                          {limitation}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Right side - Price and status */}
            <div className="bg-muted/30 p-6 md:w-1/3 flex flex-col justify-between border-t md:border-t-0 md:border-l">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium">Status</span>
                  {initialData?.isActive ? (
                    <Badge
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200"
                    >
                      Aktif
                    </Badge>
                  ) : (
                    <Badge
                      variant="outline"
                      className="bg-red-50 text-red-700 border-red-200"
                    >
                      Tidak Aktif
                    </Badge>
                  )}
                </div>

                <div className="mb-6">
                  <span className="text-sm text-muted-foreground">Harga</span>
                  <div className="mt-1">
                    <span className="text-2xl font-bold">
                      {currentPlanDetails.price.split("/")[0]}
                    </span>
                    {currentPlanDetails.price.includes("/") && (
                      <span className="text-sm text-muted-foreground ml-1">
                        /{currentPlanDetails.price.split("/")[1]}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <Button
                variant="default"
                size="sm"
                className="w-full cursor-pointer"
                onClick={() => router.push("/dashboard/settings/billing")}
              >
                Kelola Langganan
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* Available Plans Section with Tabs */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Paket Tersedia</h3>
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Bandingkan Paket
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Free Plan */}
          <Card
            className={`overflow-hidden transition-all duration-200 pt-0 ${selectedPlan === "FREE" ? "border-primary ring-1 ring-primary" : "hover:border-primary/50 hover:shadow-md"}`}
          >
            <div className="relative h-full flex flex-col">
              {initialData?.plan === "FREE" && (
                <div className="absolute top-0 right-0 left-0 bg-primary text-primary-foreground text-xs py-1 text-center">
                  Paket Saat Ini
                </div>
              )}
              <CardHeader
                className={`${initialData?.plan === "FREE" ? "pt-8" : ""}`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <Package className="h-4 w-4 text-gray-600" />
                  </div>
                  <CardTitle>{SUBSCRIPTION_PLANS.FREE.name}</CardTitle>
                </div>
                <CardDescription>
                  {SUBSCRIPTION_PLANS.FREE.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 flex-grow">
                <div>
                  <p className="text-3xl font-bold">Rp 0</p>
                  <p className="text-sm text-muted-foreground">Selamanya</p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <ul className="space-y-2">
                    {SUBSCRIPTION_PLANS.FREE.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  {SUBSCRIPTION_PLANS.FREE.limitations.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-dashed">
                      {SUBSCRIPTION_PLANS.FREE.limitations.map(
                        (limitation, index) => (
                          <div
                            key={index}
                            className="flex items-start gap-2 mt-2"
                          >
                            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                            <span className="text-sm text-muted-foreground">
                              {limitation}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/20 border-t p-4 mt-auto m-2">
                <Button
                  variant={initialData?.plan === "FREE" ? "outline" : "default"}
                  className="w-full cursor-pointer"
                  disabled={initialData?.plan === "FREE"}
                  onClick={() => handleSubscribe("FREE")}
                >
                  {initialData?.plan === "FREE"
                    ? "Paket Saat Ini"
                    : "Pilih Paket"}
                </Button>
              </CardFooter>
            </div>
          </Card>

          {/* Basic Plan */}
          <Card
            className={`overflow-hidden transition-all duration-200 ${selectedPlan === "BASIC" ? "border-primary ring-1 ring-primary" : "hover:border-primary/50 hover:shadow-md"}`}
          >
            <div className="relative h-full flex flex-col">
              {initialData?.plan === "BASIC" && (
                <div className="absolute top-0 right-0 left-0 bg-primary text-primary-foreground text-xs py-1 text-center">
                  Paket Saat Ini
                </div>
              )}
              <CardHeader
                className={`${initialData?.plan === "BASIC" ? "pt-8" : ""}`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <Users className="h-4 w-4 text-blue-600" />
                  </div>
                  <CardTitle>{SUBSCRIPTION_PLANS.BASIC.name}</CardTitle>
                </div>
                <CardDescription>
                  {SUBSCRIPTION_PLANS.BASIC.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 flex-grow">
                <div>
                  <p className="text-3xl font-bold">
                    Rp {SUBSCRIPTION_PLANS.BASIC.price.toLocaleString("id-ID")}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {SUBSCRIPTION_PLANS.BASIC.period}
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <ul className="space-y-2">
                    {SUBSCRIPTION_PLANS.BASIC.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  {SUBSCRIPTION_PLANS.BASIC.limitations.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-dashed">
                      {SUBSCRIPTION_PLANS.BASIC.limitations.map(
                        (limitation, index) => (
                          <div
                            key={index}
                            className="flex items-start gap-2 mt-2"
                          >
                            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                            <span className="text-sm text-muted-foreground">
                              {limitation}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/20 border-t p-4 mt-auto m-2">
                <Button
                  variant={
                    initialData?.plan === "BASIC" ? "outline" : "default"
                  }
                  className="w-full cursor-pointer"
                  disabled={initialData?.plan === "BASIC"}
                  onClick={() => handleSubscribe("BASIC")}
                >
                  {initialData?.plan === "BASIC"
                    ? "Paket Saat Ini"
                    : "Pilih Paket"}
                </Button>
              </CardFooter>
            </div>
          </Card>

          {/* Pro Plan */}
          <Card
            className={`overflow-hidden transition-all duration-200 pt-0 ${selectedPlan === "PRO" ? "border-primary ring-1 ring-primary" : "hover:border-primary/50 hover:shadow-md"}`}
          >
            <div className="relative h-full flex flex-col">
              {initialData?.plan === "PRO" && (
                <div className="absolute top-0 right-0 left-0 bg-primary text-primary-foreground text-xs py-1 text-center">
                  Paket Saat Ini
                </div>
              )}
              {initialData?.plan !== "PRO" && (
                <div className="absolute top-0 right-0 left-0 bg-orange-500 text-white text-xs py-1 text-center">
                  Populer
                </div>
              )}
              <CardHeader className="pt-8">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                    <Zap className="h-4 w-4 text-orange-600" />
                  </div>
                  <CardTitle>{SUBSCRIPTION_PLANS.PRO.name}</CardTitle>
                </div>
                <CardDescription>
                  {SUBSCRIPTION_PLANS.PRO.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 flex-grow">
                <div>
                  <p className="text-3xl font-bold">
                    Rp {SUBSCRIPTION_PLANS.PRO.price.toLocaleString("id-ID")}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {SUBSCRIPTION_PLANS.PRO.period}
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <ul className="space-y-2">
                    {SUBSCRIPTION_PLANS.PRO.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  {SUBSCRIPTION_PLANS.PRO.limitations.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-dashed">
                      {SUBSCRIPTION_PLANS.PRO.limitations.map(
                        (limitation, index) => (
                          <div
                            key={index}
                            className="flex items-start gap-2 mt-2"
                          >
                            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                            <span className="text-sm text-muted-foreground">
                              {limitation}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/20 border-t p-4 mt-auto m-2">
                <Button
                  variant={initialData?.plan === "PRO" ? "outline" : "default"}
                  className="w-full cursor-pointer"
                  disabled={initialData?.plan === "PRO"}
                  onClick={() => handleSubscribe("PRO")}
                >
                  {initialData?.plan === "PRO"
                    ? "Paket Saat Ini"
                    : "Pilih Paket"}
                </Button>
              </CardFooter>
            </div>
          </Card>

          {/* Enterprise Plan */}
          <Card
            className={`overflow-hidden transition-all duration-200 pt-0 ${selectedPlan === "ENTERPRISE" ? "border-primary ring-1 ring-primary" : "hover:border-primary/50 hover:shadow-md"}`}
          >
            <div className="relative h-full flex flex-col">
              {initialData?.plan === "ENTERPRISE" && (
                <div className="absolute top-0 right-0 left-0 bg-primary text-primary-foreground text-xs py-1 text-center">
                  Paket Saat Ini
                </div>
              )}
              {initialData?.plan !== "ENTERPRISE" && (
                <div className="absolute top-0 right-0 left-0 bg-purple-600 text-white text-xs py-1 text-center">
                  Premium
                </div>
              )}
              <CardHeader className="pt-8">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <Shield className="h-4 w-4 text-purple-600" />
                  </div>
                  <CardTitle>{SUBSCRIPTION_PLANS.ENTERPRISE.name}</CardTitle>
                </div>
                <CardDescription>
                  {SUBSCRIPTION_PLANS.ENTERPRISE.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 flex-grow">
                <div>
                  <p className="text-3xl font-bold">
                    Rp{" "}
                    {SUBSCRIPTION_PLANS.ENTERPRISE.price.toLocaleString(
                      "id-ID"
                    )}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {SUBSCRIPTION_PLANS.ENTERPRISE.period}
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <ul className="space-y-2">
                    {SUBSCRIPTION_PLANS.ENTERPRISE.features.map(
                      (feature, index) => (
                        <li
                          key={index}
                          className="flex items-start gap-2 text-sm"
                        >
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      )
                    )}
                  </ul>
                  {SUBSCRIPTION_PLANS.ENTERPRISE.limitations.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-dashed">
                      {SUBSCRIPTION_PLANS.ENTERPRISE.limitations.map(
                        (limitation, index) => (
                          <div
                            key={index}
                            className="flex items-start gap-2 mt-2"
                          >
                            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                            <span className="text-sm text-muted-foreground">
                              {limitation}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/20 border-t p-4 mt-auto m-2">
                <Button
                  variant={
                    initialData?.plan === "ENTERPRISE" ? "outline" : "default"
                  }
                  className="w-full cursor-pointer"
                  disabled={initialData?.plan === "ENTERPRISE"}
                  onClick={() => handleSubscribe("ENTERPRISE")}
                >
                  {initialData?.plan === "ENTERPRISE"
                    ? "Paket Saat Ini"
                    : "Pilih Paket"}
                </Button>
              </CardFooter>
            </div>
          </Card>
        </div>

        {/* Plan Comparison Note */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
          <div className="flex items-start gap-3">
            <div className="mt-0.5">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                Butuh bantuan memilih paket?
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
                Hubungi tim dukungan kami untuk mendapatkan rekomendasi paket
                yang sesuai dengan kebutuhan bisnis Anda.
              </p>
              <Button
                variant="link"
                className="text-blue-700 dark:text-blue-400 p-0 h-auto mt-2 text-xs cursor-pointer"
                onClick={() => router.push("/dashboard/settings/support")}
              >
                Hubungi Dukungan
              </Button>
            </div>
          </div>
        </div>

        {/* Payment Methods Section */}
        <div className="space-y-4 mt-8">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Metode Pembayaran</h3>
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200"
            >
              Aman & Terpercaya
            </Badge>
          </div>

          <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 border-b">
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-primary" />
                <CardTitle className="text-base">
                  Metode Pembayaran Tersedia
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">Transfer Bank</p>
                    <p className="text-sm text-muted-foreground">
                      BCA, BNI, Mandiri, BRI
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium">E-Wallet</p>
                    <p className="text-sm text-muted-foreground">
                      OVO, GoPay, DANA, LinkAja
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/20 border-t p-4">
              <Button
                variant="outline"
                className="w-full cursor-pointer"
                onClick={() => router.push("/dashboard/settings/billing")}
              >
                Kelola Metode Pembayaran
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
